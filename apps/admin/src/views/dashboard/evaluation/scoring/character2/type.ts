/**
 * 五维性格测评 2.0 相关类型定义
 */

// 基础参数接口
export interface Character2BaseParams {
    paramA: number;
    paramB: number;
    scoreMin: number;
    scoreMax: number;
}

// 维度信息接口
export interface DimensionItem {
    encryptId: string;
    name: string;
    dimensionLevel: number;
}

// 配置组接口
export interface ConfigGroup {
    id: string;
    name: string;
}

// 权重表行数据接口
export interface WeightTableRow {
    dimensionName: string;
    encryptDimensionId: string;
    [key: string]: any; // 动态权重字段 weight_${groupId}
}

// 常模表行数据接口
export interface NormTableRow {
    groupId: string;
    groupName: string;
    avgScore: number;
    stdDev: number;
}

// 组件内部表单数据接口
export interface ComponentFormData {
    configGroups: ConfigGroup[];
    weightTableData: WeightTableRow[];
    normTableData: NormTableRow[];
}

// API 返回的行数据接口
export interface ApiRowData {
    encDimensionId: string;
    dimensionName: string;
    define?: string | null;
    weight: number;
}

// API 返回的配置项接口
export interface ApiConfigItem {
    encId: string;
    headName: string;
    normalAverageScore: number; // 移动到 ApiConfigItem
    normalStandardDeviation: number; // 移动到 ApiConfigItem
    rowDataList: ApiRowData[];
}

// 团队角色配置接口
export interface TeamRoleConfig {
    id: string;
    name: string;
    description?: string;
}

// 团队角色权重项接口
export interface TeamRoleWeightItem {
    id: string;
    name: string;
    weight: number;
}

// 团队角色常模项接口
export interface TeamRoleNormItem {
    id: string;
    name: string;
    avgScore: number;
    stdDev: number;
}

// 完整的 paramC 数据结构
export interface ParamCData {
    scoreMin: number;
    scoreMax: number;
    levelArray: number[];
    keyPotentialQualityList: ApiConfigItem[];
    positionQualityModelMatchList: ApiConfigItem[];
    teamRoleList: ApiConfigItem[];
}

// 完整的详情数据接口
export interface Character2DetailData {
    paramA: number;
    paramB: number;
    paramC: ParamCData;
    fileParam?: {
        product21PercentLevelScoreMatch?: {
            name: string;
            url: string;
            [key: string]: any;
        };
        [key: string]: any;
    };
}

// 保存参数接口
export interface SaveParams {
    productId: string | number;
    paramA: number;
    paramB: number;
    paramC: ParamCData;
}

// 表单验证规则接口
export interface ValidationRule {
    required?: boolean;
    type?: 'string' | 'number' | 'boolean' | 'object' | 'array' | 'email' | 'url' | 'ip';
    message: string;
    validator?: (value: any, callback: (message?: string) => void) => void;
}

// 表格列配置接口
export interface TableColumn {
    label: string;
    field: string;
    width?: number;
}

// 组件暴露的方法接口
export interface ComponentExpose {
    validate: () => Promise<boolean>;
    getCurrentFormData: () => ComponentFormData;
}

// 错误处理接口
export interface ApiError {
    code: number;
    message: string;
    data?: any;
}

// API 响应接口
export interface ApiResponse<T = any> {
    code: number;
    message?: string;
    data?: T;
}

// 表单状态接口
export interface FormState {
    loading: boolean;
    saving: boolean;
    validating: boolean;
    errors: Record<string, string>;
}

// 权重验证状态接口
export interface WeightValidationState {
    hasError: boolean;
    errorGroups: string[];
    errorMessage: string;
}

// 组件配置接口
export interface ComponentConfig {
    maxConfigGroups: number;
    defaultGroupName: string;
    weightPrecision: number;
    normPrecision: number;
    weightMin: number;
    weightMax: number;
    normMin: number;
    normMax: number;
    normStdDevMin: number;
}

// 等级标签配置接口
export interface LevelLabel {
    label: string;
    hide: boolean;
}

// 导出表格数据接口
export interface ExportTableData {
    name: string;
    code: string;
    url?: string;
    [key: string]: any;
}

// 状态管理接口
export interface Character2State {
    // 基础数据
    baseParams: Character2BaseParams;
    levelData: number[];

    // 组件数据
    keyPotentialQualityData: ApiConfigItem[];
    jobQualityModelData: ApiConfigItem[];
    teamRoleData: TeamRoleConfig[];

    // 维度数据
    dimensionList: DimensionItem[];

    // 表单状态
    formState: FormState;

    // 验证状态
    weightValidationState: WeightValidationState;

    // 其他状态
    currentTab: number;
    dataChanged: boolean;
    saved: boolean;
}

// 操作类型枚举
export enum ActionType {
    LOAD_DETAIL = 'LOAD_DETAIL',
    SAVE_PARAMS = 'SAVE_PARAMS',
    UPDATE_BASE_PARAMS = 'UPDATE_BASE_PARAMS',
    UPDATE_LEVEL_DATA = 'UPDATE_LEVEL_DATA',
    UPDATE_KEY_POTENTIAL = 'UPDATE_KEY_POTENTIAL',
    UPDATE_JOB_QUALITY = 'UPDATE_JOB_QUALITY',
    UPDATE_TEAM_ROLE = 'UPDATE_TEAM_ROLE',
    VALIDATE_FORM = 'VALIDATE_FORM',
    RESET_FORM = 'RESET_FORM',
}

// 常量配置
export const CHARACTER2_CONSTANTS = {
    PRODUCT_ID: 21,
    DIMENSION_LEVEL: 2,
    MAX_CONFIG_GROUPS: 16,
    WEIGHT_PRECISION: 4,
    NORM_PRECISION: 4,
    PARAM_MIN: 1,
    PARAM_MAX: 99,
    SCORE_MIN: 1,
    SCORE_MAX: 99,
    WEIGHT_MIN: 0,
    WEIGHT_MAX: 1,
    NORM_MIN: 0,
    NORM_MAX: 1000,
    NORM_STD_DEV_MIN: 0.0001,
    LEVEL_MIN: 0.1,
    LEVEL_MAX: 99.9,
    LEVEL_PRECISION: 1,
    DEFAULT_TEAM_ROLES: ['实干者', '协调者', '推进者', '创新者', '信息者', '监督者', '凝聚者', '完善者', '专业师'],
} as const;
