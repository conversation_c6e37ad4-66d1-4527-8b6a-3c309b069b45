/**
 * 五维性格测评 2.0 表单逻辑组合式函数
 */

import { ref, computed, watch, nextTick } from 'vue';
import { useRoute } from 'vue-router';
import type { ConfigGroup, WeightTableRow, NormTableRow, DimensionItem, TableColumn, ComponentFormData, ApiConfigItem } from '../type';
import { CHARACTER2_CONSTANTS } from '../type';

export function useCharacter2Form(dimensionList: DimensionItem[]) {
    const route = useRoute();

    // 内部状态
    const configGroups = ref<ConfigGroup[]>([{ id: '1', name: '配置组1' }]);
    const weightTableData = ref<WeightTableRow[]>([]);
    const normTableData = ref<NormTableRow[]>([]);

    // 表格列配置
    const weightColumns = ref<TableColumn[]>([]);
    const normColumns = ref<TableColumn[]>([]);

    // 计算属性
    const productId = computed(() => route.query?.productId || CHARACTER2_CONSTANTS.PRODUCT_ID);

    const canAddConfigGroup = computed(() => configGroups.value.length < CHARACTER2_CONSTANTS.MAX_CONFIG_GROUPS);

    const canRemoveConfigGroup = computed(() => configGroups.value.length > 1);

    /**
     * 初始化表格数据
     */
    function initTableData() {
        // 初始化权重表数据
        weightTableData.value = dimensionList.map((dimension) => {
            const rowData: WeightTableRow = {
                dimensionName: dimension.name,
                encryptDimensionId: dimension.encryptId,
            };

            // 为每个配置组添加权重字段
            configGroups.value.forEach((group) => {
                rowData[`weight_${group.id}`] = 0;
            });

            return rowData;
        });

        // 初始化常模表数据
        normTableData.value = configGroups.value.map((group) => ({
            groupId: group.id,
            groupName: group.name,
            avgScore: 0,
            stdDev: 0,
        }));

        updateColumns();
    }

    /**
     * 更新列配置
     */
    function updateColumns(tableType: 'key-potential' | 'job-quality' | 'team-role' = 'key-potential') {
        // 更新权重表列配置
        weightColumns.value = [
            {
                label: '二级维度',
                field: 'dimension',
                width: 200,
            },
            ...configGroups.value.map((group) => ({
                label: group.name,
                field: `weight_${group.id}`,
                width: 160,
            })),
        ];

        // 更新常模表列配置 - 根据表格类型设置不同的标签
        let firstColumnLabel = '关键潜在素质';
        if (tableType === 'job-quality') {
            firstColumnLabel = '岗位素质模型';
        } else if (tableType === 'team-role') {
            firstColumnLabel = '团队角色';
        }

        normColumns.value = [
            {
                label: firstColumnLabel,
                field: 'groupName',
                width: 200,
            },
            {
                label: '常模平均分',
                field: 'avgScore',
                width: 150,
            },
            {
                label: '常模标准差',
                field: 'stdDev',
                width: 150,
            },
        ];
    }

    /**
     * 初始化团队角色配置组
     */
    function initTeamRoleConfigGroups() {
        configGroups.value = CHARACTER2_CONSTANTS.DEFAULT_TEAM_ROLES.map((roleName, index) => ({
            id: (index + 1).toString(),
            name: roleName,
        }));
    }

    /**
     * 添加配置组
     */
    function addConfigGroup() {
        if (!canAddConfigGroup.value) return;

        const newId = Date.now().toString();
        const newGroup: ConfigGroup = {
            id: newId,
            name: `配置组${configGroups.value.length + 1}`,
        };

        configGroups.value.push(newGroup);

        // 为现有权重表数据添加新配置组的字段
        weightTableData.value.forEach((row) => {
            row[`weight_${newId}`] = 0;
        });

        // 为常模表添加新的配置组行
        normTableData.value.push({
            groupId: newId,
            groupName: newGroup.name,
            avgScore: 0,
            stdDev: 0,
        });

        updateColumns();
    }

    /**
     * 删除配置组
     */
    function removeConfigGroup(groupId: string) {
        if (!canRemoveConfigGroup.value) return;

        const index = configGroups.value.findIndex((group) => group.id === groupId);
        if (index > -1) {
            configGroups.value.splice(index, 1);

            // 从权重表数据中移除对应字段
            weightTableData.value.forEach((row) => {
                delete row[`weight_${groupId}`];
            });

            // 从常模表中移除对应的行
            const normIndex = normTableData.value.findIndex((row) => row.groupId === groupId);
            if (normIndex > -1) {
                normTableData.value.splice(normIndex, 1);
            }

            updateColumns();
        }
    }

    /**
     * 从API数据初始化内部状态
     */
    function initFromApiData(apiData: ApiConfigItem[]) {
        try {
            if (!Array.isArray(apiData) || apiData.length === 0) {
                return;
            }

            // 重建配置组
            const groups: ConfigGroup[] = [];
            const normData: NormTableRow[] = [];

            // 处理每个配置组
            apiData.forEach((item, index) => {
                if (item.headName) {
                    const groupId = item.encId || (index + 1).toString();

                    // 添加配置组
                    groups.push({
                        id: groupId,
                        name: item.headName,
                    });

                    // 处理常模数据
                    normData.push({
                        groupId: groupId,
                        groupName: item.headName,
                        avgScore: item.normalAverageScore || 0,
                        stdDev: item.normalStandardDeviation || 0,
                    });
                }
            });

            // 重建权重表数据
            const weightData: WeightTableRow[] = [];
            if (apiData[0]?.rowDataList && Array.isArray(apiData[0].rowDataList)) {
                apiData[0].rowDataList.forEach((row) => {
                    const weightRow: WeightTableRow = {
                        dimensionName: row.dimensionName,
                        encryptDimensionId: row.encDimensionId,
                    };

                    // 为每个配置组添加权重字段
                    apiData.forEach((item, itemIndex) => {
                        const groupId = item.encId || (itemIndex + 1).toString();
                        const rowData = item.rowDataList?.find((r) => r.encDimensionId === row.encDimensionId);
                        weightRow[`weight_${groupId}`] = rowData?.weight || 0;
                    });

                    weightData.push(weightRow);
                });
            }

            // 更新内部状态
            if (groups.length > 0) {
                configGroups.value = groups;
                weightTableData.value = weightData;
                normTableData.value = normData;
                updateColumns();
            }
        } catch (error) {
            // eslint-disable-next-line no-console
            console.error('从API数据初始化失败:', error);
        }
    }

    /**
     * 获取当前表单数据
     */
    function getCurrentFormData(): ComponentFormData {
        return {
            configGroups: configGroups.value,
            weightTableData: weightTableData.value,
            normTableData: normTableData.value,
        };
    }

    /**
     * 重置表单数据
     */
    function resetFormData() {
        configGroups.value = [{ id: '1', name: '配置组1' }];
        weightTableData.value = [];
        normTableData.value = [];
        initTableData();
    }

    // 监听配置组名称变化，同步到常模表
    watch(
        () => configGroups.value.map((group) => ({ id: group.id, name: group.name })),
        (newGroups) => {
            newGroups.forEach((group) => {
                const normItem = normTableData.value.find((item) => item.groupId === group.id);
                if (normItem) {
                    normItem.groupName = group.name;
                }
            });
        },
        { deep: true }
    );

    return {
        // 状态
        configGroups,
        weightTableData,
        normTableData,
        weightColumns,
        normColumns,

        // 计算属性
        productId,
        canAddConfigGroup,
        canRemoveConfigGroup,

        // 方法
        initTableData,
        initTeamRoleConfigGroups,
        updateColumns,
        addConfigGroup,
        removeConfigGroup,
        initFromApiData,
        getCurrentFormData,
        resetFormData,
    };
}
