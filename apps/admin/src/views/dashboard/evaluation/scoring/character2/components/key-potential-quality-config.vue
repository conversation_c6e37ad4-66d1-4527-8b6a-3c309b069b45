<template>
    <div class="key-potential-quality-config">
        <b-form ref="formRef" :model="{ configGroups, weightTableData, normTableData }" layout="vertical" :scrollToFirstError="{ block: 'center' }">
            <div class="sub-title">
                <h4>关键潜在素质分数计算</h4>
            </div>

            <!-- 关键潜在素质分数权重表 -->
            <div class="section-title">
                <h5>关键潜在素质分数权重表</h5>
            </div>
            <b-table border fitWidth :columns="weightColumns" :tableData="weightTableData">
                <template v-for="(col, index) in weightColumns" :key="col.field" #[`th-${col.field}`]>
                    <div v-if="index === 0" class="reference">
                        <div style="white-space: nowrap">二级维度</div>
                        <b-button v-if="canAddConfigGroup" type="text" size="xsmall" status="primary" font="medium" @click="addConfigGroup"> + 添加一列 </b-button>
                    </div>
                    <div v-else class="reference">
                        <FormField :field="`configGroups.${index - 1}.name`" hideLabel :rules="getConfigGroupNameRules()">
                            <b-input v-model.trim="configGroups[index - 1].name" placeholder="请输入配置组名称" :maxLength="10" />
                        </FormField>
                        <b-action v-if="canRemoveConfigGroup" @click="removeConfigGroup(configGroups[index - 1].id)">
                            <SvgIcon name="delete-2" />
                        </b-action>
                    </div>
                </template>
                <template v-for="(col, index) in weightColumns" :key="col.field" #[`td-${col.field}`]="{ raw, $index }">
                    <div v-if="index === 0">
                        {{ raw.dimensionName }}
                    </div>
                    <div v-else>
                        <FormField :field="`weightTableData.${$index}.${col.field}`" hideLabel :rules="getWeightRules($index, col.field)">
                            <b-input-number
                                v-model="raw[col.field]"
                                placeholder="请输入权重"
                                hideButton
                                :min="CHARACTER2_CONSTANTS.WEIGHT_MIN"
                                :max="CHARACTER2_CONSTANTS.WEIGHT_MAX"
                                :precision="CHARACTER2_CONSTANTS.WEIGHT_PRECISION"
                                @change="handleWeightChange"
                            />
                        </FormField>
                    </div>
                </template>
            </b-table>

            <!-- 关键潜在素质常模 -->
            <div class="section-title">
                <h5>关键潜在素质常模</h5>
            </div>

            <b-table border fitWidth :columns="normColumns" :tableData="normTableData">
                <template v-for="(col, index) in normColumns" :key="col.field" #[`th-${col.field}`]>
                    <div v-if="index === 0" class="reference">
                        <div style="white-space: nowrap">关键潜在素质</div>
                    </div>
                    <div v-else-if="index === 1" class="reference">
                        <div style="white-space: nowrap">常模平均分</div>
                    </div>
                    <div v-else class="reference">
                        <div style="white-space: nowrap">常模标准差</div>
                    </div>
                </template>
                <template v-for="(col, index) in normColumns" :key="col.field" #[`td-${col.field}`]="{ raw, $index }">
                    <div v-if="index === 0">
                        {{ raw.groupName }}
                    </div>
                    <div v-else-if="index === 1">
                        <FormField :field="`normTableData.${$index}.avgScore`" hideLabel :rules="getNormRules('avgScore')">
                            <b-input-number
                                v-model="raw.avgScore"
                                placeholder="请填写0-1000内数值，最多4位小数"
                                hideButton
                                :min="CHARACTER2_CONSTANTS.NORM_MIN"
                                :max="CHARACTER2_CONSTANTS.NORM_MAX"
                                :precision="CHARACTER2_CONSTANTS.NORM_PRECISION"
                            />
                        </FormField>
                    </div>
                    <div v-else>
                        <FormField :field="`normTableData.${$index}.stdDev`" hideLabel :rules="getNormRules('stdDev')">
                            <b-input-number
                                v-model="raw.stdDev"
                                placeholder="请填写1000以内正数，最多4位小数"
                                hideButton
                                :min="CHARACTER2_CONSTANTS.NORM_STD_DEV_MIN"
                                :max="CHARACTER2_CONSTANTS.NORM_MAX"
                                :precision="CHARACTER2_CONSTANTS.NORM_PRECISION"
                            />
                        </FormField>
                    </div>
                </template>
            </b-table>
        </b-form>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import type { _FormComponent } from '@boss/design';
import { FormField } from '@crm/web-components';
import type { ApiConfigItem, DimensionItem } from '../type';
import { CHARACTER2_CONSTANTS } from '../type';
import { useCharacter2Form } from '../composables/useCharacter2Form';
import { useCharacter2Validation } from '../composables/useCharacter2Validation';
import { buildKeyPotentialQualityList } from '../api';

const props = defineProps<{
    modelValue: ApiConfigItem[];
    dimensionList: DimensionItem[];
}>();

const emit = defineEmits<{
    'update:modelValue': [value: ApiConfigItem[]];
    validate: [callback: () => Promise<boolean>];
}>();

const formRef = ref<_FormComponent>();

// 使用表单逻辑组合式函数
const {
    configGroups,
    weightTableData,
    normTableData,
    weightColumns,
    normColumns,
    canAddConfigGroup,
    canRemoveConfigGroup,
    addConfigGroup,
    removeConfigGroup,
    initFromApiData,
    getCurrentFormData,
    updateColumns,
    initTableData,
} = useCharacter2Form(props.dimensionList);

// 使用验证组合式函数
const { validateForm, getWeightRules, getNormRules, getConfigGroupNameRules } = useCharacter2Validation(formRef);

// 处理权重变化
function handleWeightChange() {
    emitFormData();
}

// 发送表单数据到父组件
function emitFormData() {
    const formData = getCurrentFormData();
    const apiData = buildKeyPotentialQualityList(formData);
    emit('update:modelValue', apiData);
}

// 暴露校验方法和数据获取方法给父组件
defineExpose({
    validate: validateForm,
    getCurrentFormData,
});

// 监听父组件传入的数据变化
watch(
    () => props.modelValue,
    (newValue) => {
        if (Array.isArray(newValue)) {
            if (newValue.length > 0) {
                initFromApiData(newValue);
            } else {
                // 如果是空数组，重新初始化为默认配置
                initTableData();
                updateColumns('key-potential');
            }
        }
    },
    { immediate: true, deep: true }
);
</script>

<style lang="less" scoped>
.key-potential-quality-config {
    margin: 20px 0;
}

.sub-title {
    margin: 20px 0 10px 0;

    h4 {
        font-size: 14px;
        font-weight: 600;
        color: #1d2129;
        margin: 0;
    }
}

.section-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 15px 0 10px 0;

    h5 {
        font-size: 13px;
        font-weight: 500;
        color: #1d2129;
        margin: 0;
    }
}

.error-tip {
    color: #f53f3f;
    font-size: 12px;
    margin-top: 5px;
}

.reference {
    gap: var(--size-3);
    .w-full;
    .group;
}

.center {
    display: flex;
    justify-content: center;
    align-items: center;
}
</style>
